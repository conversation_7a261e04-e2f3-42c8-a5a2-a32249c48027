package ch.mks.wta4.services.dao.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.interceptor.SimpleKey;
import org.springframework.stereotype.Component;

import ch.mks.wta4.configuration.model.CurrencyPair;
import ch.mks.wta4.configuration.model.Location;
import ch.mks.wta4.configuration.model.PipConfiguration;
import ch.mks.wta4.ita.model.TradingStatus;
import ch.mks.wta4.services.dao.entity.CurrencyPairTbl;
import ch.mks.wta4.services.dao.repo.CurrencyPairRepository;

@Component
public class CurrencyPairService {

	static final Logger LOG = LoggerFactory.getLogger(CurrencyPairService.class);

	private final CurrencyPairRepository currencyPairRepository;

	private final CurrencyService currencyService;

	private final CacheManager cacheManager;	

	public CurrencyPairService(CurrencyPairRepository currencyPairRepository, CurrencyService currencyService,
            CacheManager cacheManager) {
        this.currencyPairRepository = currencyPairRepository;
        this.currencyService = currencyService;
        this.cacheManager = cacheManager;
    }

    private Map<String, CurrencyPairTbl> currencyPairTblMap = null;

	public Map<String, CurrencyPairTbl> getCurrencyPairTblMap() {
		return currencyPairTblMap;
	}

	public void setCurrencyPairTblMap(Map<String, CurrencyPairTbl> currencyPairTblMap) {
		this.currencyPairTblMap = currencyPairTblMap;
	}

	@Cacheable("getAllCurrencyPairs")
	public List<String> getAllCurrencyPairsIds() {
		LOG.info("getAllCurrencyPairsIds-> ");
		List<String> currencyPairList = currencyPairRepository.getAllCurrencyPairsIds();
		LOG.info("getAllCurrencyPairsIds <- currencyPairList={}", currencyPairList);
		return currencyPairList;

	}

    @Cacheable("getCurrencyPair")
    public CurrencyPair getCurrencyPair(String currencyPairId) {
        LOG.info("getCurrencyPair -> Cache miss, loading from DB: '{}'", currencyPairId);

        // Debug: Check if cache contains this key
        Cache cpCache = cacheManager.getCache("getCurrencyPair");
        if (cpCache != null) {
            Cache.ValueWrapper cachedValue = cpCache.get(currencyPairId);
            LOG.info("getCurrencyPair -> Direct cache lookup for '{}': {}", currencyPairId,
                cachedValue != null ? "FOUND" : "NOT_FOUND");
        }

        CurrencyPairTbl mainTbl = currencyPairRepository.findByCurrencyPairFldWithDetails(currencyPairId);
        if (mainTbl == null) {
            LOG.warn("No CurrencyPairTbl found for ID: {}. Returning null.", currencyPairId);
            return null;
        }

        CurrencyPairTbl derivedTbl = null;
        if (mainTbl.getDerivedCurrencyPairIdFld() != null) {
            derivedTbl = currencyPairRepository.findByOidPkfldWithDetails(mainTbl.getDerivedCurrencyPairIdFld());
        }

        // Convert the database entities into a canonical DTO.
        CurrencyPair canonical = convertCurrencyPair(mainTbl, derivedTbl);
        
        // Return a defensive copy, to protect against mutating the original cp in case of cache eviction
        return new CurrencyPair(canonical);
    }
	
    public void initCurrencyPairCache() {
        LOG.info("initCurrencyPairCache -> initializing cache");

        // Load the entire graph of entities into memory.
        Map<Integer, CurrencyPairTbl> loadedEntities = new HashMap<>();
        List<Integer> allInitialIds = currencyPairRepository.findAllOids();
        if (allInitialIds.isEmpty()) {
            return;
        }
        Set<Integer> idsToFetch = new HashSet<>(allInitialIds);
        int level = 0;
        while (!idsToFetch.isEmpty()) {
            level++;
            LOG.info("Loading entity graph level {} ({} pairs)...", level, idsToFetch.size());
            List<CurrencyPairTbl> fetchedThisRound = currencyPairRepository.findByIdInWithFirstLevelLegs(new ArrayList<>(idsToFetch));
            
            idsToFetch.clear();
            for (CurrencyPairTbl pair : fetchedThisRound) {
                if (loadedEntities.containsKey(pair.getOidPkfld())) continue;
                loadedEntities.put(pair.getOidPkfld(), pair);
                if (pair.getCurrencyPairleg1FKFld() != null && !loadedEntities.containsKey(pair.getCurrencyPairleg1FKFld().getOidPkfld())) {
                    idsToFetch.add(pair.getCurrencyPairleg1FKFld().getOidPkfld());
                }
                if (pair.getCurrencyPairleg2FKFld() != null && !loadedEntities.containsKey(pair.getCurrencyPairleg2FKFld().getOidPkfld())) {
                    idsToFetch.add(pair.getCurrencyPairleg2FKFld().getOidPkfld());
                }
            }
        }
        LOG.info("Entity graph loading complete. All {} entities are in memory.", loadedEntities.size());

        // Convert, Link, and Populate Caches
        Cache cpCache = cacheManager.getCache("getCurrencyPair");
        Cache allCpCache = cacheManager.getCache("getAllCurrencyPairs");
        List<String> allCurrencyPairIds = new ArrayList<>();

        LOG.info("initCurrencyPairCache - Starting cache population for {} entities", loadedEntities.size());

        for (CurrencyPairTbl tbl : loadedEntities.values()) {
            // Look up the derived entity from the map we already built.
            CurrencyPairTbl derivedTbl = null;
            if (tbl.getDerivedCurrencyPairIdFld() != null) {
                derivedTbl = loadedEntities.get(tbl.getDerivedCurrencyPairIdFld());
            }

            // Call the same, unified, safe converter.
            CurrencyPair finalPair = convertCurrencyPair(tbl, derivedTbl);

            if (finalPair != null) {
                String cacheKey = finalPair.getCurrencyPairId();
                // Populate with a defensive copy here as well
                cpCache.put(cacheKey, new CurrencyPair(finalPair));
                allCurrencyPairIds.add(cacheKey);
                LOG.debug("initCurrencyPairCache - Cached currency pair with key: '{}'", cacheKey);
            }
        }

        allCpCache.put(SimpleKey.EMPTY, allCurrencyPairIds);
        LOG.info("initCurrencyPairCache <- CurrencyPair caches populated with {} entries", allCurrencyPairIds.size());

        // Log first few cache keys for debugging
        if (!allCurrencyPairIds.isEmpty()) {
            LOG.info("initCurrencyPairCache - Sample cache keys: {}",
                allCurrencyPairIds.stream().limit(5).collect(java.util.stream.Collectors.toList()));
        }
    }

	@Cacheable("getCurrencyPairIdBySymbolAndLocation")
	public String getCurrencyPairIdBySymbolAndLocation(String symbol, String location) {
		LOG.info("getCurrencyPairIdBySymbolAndLocation-> symbol={},location()", symbol, location);
		String currencyPair = currencyPairRepository.getCurrencyPairIdBySymbolAndLocation(symbol, location);
		LOG.info("getCurrencyPairIdBySymbolAndLocation <- symbol={},location(),currencyPair={}", symbol, location,
				currencyPair);
		return currencyPair;
	}

	private CurrencyPair convertCurrencyPair(CurrencyPairTbl currencyPairTbl, CurrencyPairTbl derivedCurrencyPairTbl) {
	        if (currencyPairTbl == null) {
	            return null;
	        }
	        CurrencyPair currencyPair = new CurrencyPair();

	        currencyPair.setId(currencyPairTbl.getOidPkfld());
	        currencyPair.setLeftCurrency(currencyService.getCurrency(currencyPairTbl.getCurrencyTbl1().getIsoFld()));
	        currencyPair.setRightCurrency(currencyService.getCurrency(currencyPairTbl.getCurrencyTbl2().getIsoFld()));
	        currencyPair.setCurrencyPairId(currencyPairTbl.getCurrencyPairFld());
	        currencyPair.setPipConfiguration(new PipConfiguration(currencyPairTbl.getPipSizeFld().doubleValue(),
	                currencyPairTbl.getPricePrecisionFld().doubleValue()));

	        if (currencyPairTbl.getIsBlockedFld() == 'Y') {
	            currencyPair.setTradingStatus(TradingStatus.TRADING_BLOCKED);
	            currencyPair.setTradingStatusFlag(false);
	        } else {
	            currencyPair.setTradingStatus(TradingStatus.TRADABLE);
	            currencyPair.setTradingStatusFlag(true);
	        }

	        currencyPair.setHedgingQuantityPrecision(currencyPairTbl.getHedgingPrecisionFld().doubleValue());
	        currencyPair.setMinimumTradeQuantityInBaseUnits(currencyPairTbl.getMinimumQuantityFld().doubleValue());
	        currencyPair.setMaximumTradeQuantityInBaseUnits(currencyPairTbl.getMaximumQuantityFld().doubleValue());

	        if (currencyPairTbl.getStalePriceTimeoutFld() != null) {
	            currencyPair.setStaleThresholdInMillis(currencyPairTbl.getStalePriceTimeoutFld().longValue());
	        }
	        if (currencyPairTbl.getMinimumBidOfferSpreadFld() != null) {
	            currencyPair.setMinimumSpread(currencyPairTbl.getMinimumBidOfferSpreadFld().doubleValue());
	        }
	        if (currencyPairTbl.getPriceVariationThresholdFld() != null) {
	            currencyPair.setPriceVariationThreshold(currencyPairTbl.getPriceVariationThresholdFld().doubleValue());
	        }
	        if (currencyPairTbl.getPriceInputControlFld() != null) {
	            currencyPair.setPriceInputControlThreshold(currencyPairTbl.getPriceInputControlFld().doubleValue());
	        }
	        if (currencyPairTbl.getHedgingMinimumQuantityFld() != null) {
	            currencyPair.setHedgingMinimumQuantity(currencyPairTbl.getHedgingMinimumQuantityFld().doubleValue());
	        }
	        if (currencyPairTbl.getOrderLevelThresholdFld() != null) {
	            currencyPair.setOrderPriceCheckMargin(currencyPairTbl.getOrderLevelThresholdFld().doubleValue());
	        }
	        if (currencyPairTbl.getCommissionBidFld() != null) {
	            currencyPair.setAuctionCommissionBid(currencyPairTbl.getCommissionBidFld().doubleValue());
	        }
	        if (currencyPairTbl.getCommissionOfferFld() != null) {
	            currencyPair.setAuctionCommissionOffer(currencyPairTbl.getCommissionOfferFld().doubleValue());
	        }

	        // fix: This now uses the pre-fetched entity passed as an argument
	        if (derivedCurrencyPairTbl != null) {
	            currencyPair.setSyntheticBaseCurrencyPairId(derivedCurrencyPairTbl.getCurrencyPairFld());
	        }

	        if (currencyPairTbl.getDerivationFactorFld() != null) {
	            currencyPair.setSyntheticFactor(currencyPairTbl.getDerivationFactorFld().doubleValue());
	        }
	        if (currencyPairTbl.getCurrencyPairleg1FKFld() != null) {
	            currencyPair.setCrossLeg1CurrencyPairId(currencyPairTbl.getCurrencyPairleg1FKFld().getCurrencyPairFld());
	        }
	        if (currencyPairTbl.getCurrencyPairleg2FKFld() != null) {
	            currencyPair.setCrossLeg2CurrencyPairId(currencyPairTbl.getCurrencyPairleg2FKFld().getCurrencyPairFld());
	        }
	        if (currencyPairTbl.getOfflineMarkupFld() != null) {
	            currencyPair.setOfflineMarkup(currencyPairTbl.getOfflineMarkupFld().doubleValue());
	        }
	        if (currencyPairTbl.getOfflineMarkupTypeFld() != null) {
	            currencyPair.setOfflineMarkupType(CurrencyPair.OfflineMarkupType.valueOf(currencyPairTbl.getOfflineMarkupTypeFld()));
	        }
	        
	        setLocation(currencyPairTbl.getLocationFld(), currencyPair);
	        currencyPair.setSymbol(currencyPairTbl.getSymbolFld());
	        currencyPair.setDisplayName(currencyPairTbl.getDisplayNameFld());
	        
	        if (currencyPairTbl.getBasePriceComputationModeFld() != null) {
	            currencyPair.setBasePriceComputationMode(CurrencyPair.BasePriceComputationMode.valueOf(currencyPairTbl.getBasePriceComputationModeFld()));
	        }
	        if (currencyPairTbl.getLpSpreadFactorFld() != null) {
	            currencyPair.setLpSpreadFactor(currencyPairTbl.getLpSpreadFactorFld().doubleValue());
	        }
	        if (currencyPairTbl.getSpreadReductionFactorOnInternalizationFld() != null) {
	            currencyPair.setSpreadReductionFactorOnInternalization(currencyPairTbl.getSpreadReductionFactorOnInternalizationFld().doubleValue());
	        }

	        return currencyPair;
	    }

	private void setLocation(String location, CurrencyPair currencyPair) {
		if (location == null)
			return;
		switch (location) {
		case "Zurich":
			currencyPair.setLocation(Location.ZURICH);
			break;
		case "London":
			currencyPair.setLocation(Location.LONDON);
			break;
		case "Royston":
			currencyPair.setLocation(Location.ROYSTON);
			break;
		case "Shanghai":
			currencyPair.setLocation(Location.SHANGHAI);
			break;
		case "Valley forge":
			currencyPair.setLocation(Location.VALLEY_FORGE);
			break;

		default:
			currencyPair.setLocation(null);
		}
	}

}
